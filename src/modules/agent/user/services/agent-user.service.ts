import { UpdateProductResponseDto } from './../../../marketplace/user/dto/update-product-response.dto';
import {
  AgentMediaRepository,
  AgentProductRepository,
  AgentRepository,
  AgentUserRepository,
  TypeAgentRepository,
  UserMultiAgentRepository,
  AgentRankRepository
} from '@modules/agent/repositories';
import { AgentUrlRepository } from '@modules/agent/repositories/agent-url.repository';

import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import { Agent, AgentUser } from '@modules/agent/entities';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { TypeAgentConfig } from '@modules/agent/interfaces/type-agent-config.interface';
import { FacebookPageRepository, UserWebsiteRepository } from '@modules/integration/repositories';
import { Injectable, Logger } from '@nestjs/common';
import { S3Service } from '@shared/services/s3.service';
import { CdnService } from '@shared/services/cdn.service';
import { FileSizeEnum, ImageType, TimeIntervalEnum } from '@shared/utils';
import { CategoryFolderEnum, generateS3Key } from '@shared/utils/generators/s3-key-generator.util';
import { Transactional } from 'typeorm-transactional';
import {
  CreateAgentResponseDto,
  AgentQueryDto,
  AgentListItemDto
} from '../dto/agent';
import { AgentSimpleQueryDto, AgentSimpleListDto } from '../dto/agent-simple-list.dto';
import { CreateAgentDto, ResourcesBlockDto } from '../dto/agent/create-agent.dto';
import { ProfileMapper, AgentMapper } from '../mappers';
import { AgentListMapper } from '@modules/agent/mappers/agent-list.mapper';

/**
 * Service xử lý các thao tác liên quan đến agent cho người dùng
 */
@Injectable()
export class AgentUserService {
  private readonly logger = new Logger(AgentUserService.name);

  constructor(
    private readonly agentRepository: AgentRepository,
    private readonly agentUserRepository: AgentUserRepository,
    private readonly typeAgentRepository: TypeAgentRepository,
    private readonly agentMediaRepository: AgentMediaRepository,
    private readonly agentProductRepository: AgentProductRepository,
    private readonly agentUrlRepository: AgentUrlRepository,
    private readonly userMultiAgentRepository: UserMultiAgentRepository,
    private readonly facebookPageRepository: FacebookPageRepository,
    private readonly userWebsiteRepository: UserWebsiteRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
    private readonly agentRankRepository: AgentRankRepository,
    private readonly agentListMapper: AgentListMapper,
  ) { }

  /**
   * Tạo agent mới với cấu trúc modular
   * @param userId ID của người dùng
   * @param createDto Thông tin agent mới theo cấu hình TypeAgent
   * @returns Thông tin tạo agent thành công
   */
  @Transactional()
  async createAgent(
    userId: number,
    createDto: CreateAgentDto, 
  ): Promise<CreateAgentResponseDto> {
    try {
      this.logger.log(`Bắt đầu tạo agent cho user ${userId}: ${createDto.name}`);

      // 1. Lấy TypeAgent và TypeAgentConfig từ typeId
      const typeAgent = await this.typeAgentRepository.findById(createDto.typeId);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      const typeAgentConfig = typeAgent.config; // TypeAgentConfig từ trường config
      this.logger.log(`TypeAgentConfig: ${JSON.stringify(typeAgentConfig)}`);

      // 2. Validate dữ liệu dựa trên TypeAgentConfig
      await this.validateAgentDataByConfig(createDto, typeAgentConfig);

      // 3. Kiểm tra tên agent đã tồn tại cho user này chưa
      const existingAgent = await this.agentRepository.existsByNameAndUserId(createDto.name, userId);
      if (existingAgent) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NAME_EXISTS);
      }

      // 4. Tạo agent entity cơ bản
      const agent = await this.createAgentEntity(createDto, userId);

      // 5. Xử lý các blocks dữ liệu theo TypeAgentConfig
      await this.processAgentBlocks(agent, createDto, typeAgentConfig, userId);

      // 6. Tạo S3 key cho avatar nếu có
      let avatarUploadUrl: string | undefined;
      if (createDto.avatarMimeType && agent.avatar) {
        avatarUploadUrl = await this.s3Service.createPresignedWithID(agent.avatar, TimeIntervalEnum.ONE_HOUR, ImageType.getType(createDto.avatarMimeType), FileSizeEnum.FIVE_MB);
      }

      this.logger.log(`Đã tạo agent thành công: ${agent.id}`);

      return {
        id: agent.id,
        avatarUploadUrl: avatarUploadUrl || null,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi tạo agent: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_CREATION_FAILED, error.message);
    }
  }

  /**
   * Validate dữ liệu agent dựa trên TypeAgentConfig
   * @param createDto DTO tạo agent
   * @param config TypeAgentConfig
   */
  private async validateAgentDataByConfig(
    createDto: CreateAgentDto,
    config: TypeAgentConfig,
  ): Promise<void> {
    // 1. Validate logic model - BẮT BUỘC
    await this.validateModelConfiguration(createDto);

    // 2. Validate các khối theo TypeAgentConfig - CÓ THỂ TRỐNG
    // Chỉ validate khi tính năng được enable và có dữ liệu
    if (config.enableAgentProfileCustomization && createDto.profile) {
      await this.validateProfileBlock(createDto.profile);
    }

    if (config.enableTaskConversionTracking && createDto.conversion) {
      await this.validateConversionBlock(createDto.conversion);
    }

    if (config.enableDynamicStrategyExecution && createDto.strategy) {
      await this.validateStrategyBlock(createDto.strategy);
    }

    if (config.enableMultiAgentCollaboration && createDto.multiAgent) {
      await this.validateMultiAgentBlock(createDto.multiAgent);
    }

    if (config.enableOutputToMessenger && createDto.outputMessenger) {
      await this.validateOutputMessengerBlock(createDto.outputMessenger);
    }

    if (config.enableOutputToWebsiteLiveChat && createDto.outputWebsite) {
      await this.validateOutputWebsiteBlock(createDto.outputWebsite);
    }

    if (config.enableResourceUsage && createDto.resources) {
      await this.validateResourcesBlock(createDto.resources);
    }
  }

  /**
   * Validate logic model configuration
   * @param createDto DTO tạo agent
   */
  private async validateModelConfiguration(createDto: CreateAgentDto): Promise<void> {
    // Kiểm tra khối 1: systemModelId
    const hasSystemModel = createDto.systemModelId && createDto.systemModelId.trim().length > 0;

    // Kiểm tra khối 2: userModelId + keyLlmId
    const hasUserModel = createDto.userModelId && createDto.userModelId.trim().length > 0;
    const hasKeyLlm = createDto.keyLlmId && createDto.keyLlmId.trim().length > 0;
    const hasUserModelBlock = hasUserModel && hasKeyLlm;

    // Phải có ít nhất 1 trong 2 khối
    if (!hasSystemModel && !hasUserModelBlock) {
      throw new AppException(
        AGENT_ERROR_CODES.INVALID_MODEL_CONFIG,
        'Bắt buộc phải có 1 trong 2 khối: (systemModelId) hoặc (userModelId + keyLlmId)'
      );
    }

    // Nếu có userModelId thì phải có keyLlmId và ngược lại
    if (hasUserModel && !hasKeyLlm) {
      throw new AppException(
        AGENT_ERROR_CODES.INVALID_MODEL_CONFIG,
        'Nếu có userModelId thì phải có keyLlmId'
      );
    }

    if (hasKeyLlm && !hasUserModel) {
      throw new AppException(
        AGENT_ERROR_CODES.INVALID_MODEL_CONFIG,
        'Nếu có keyLlmId thì phải có userModelId'
      );
    }
  }

  /**
   * Validate Profile block - Kiểm tra thông tin hồ sơ agent
   * @param profile Dữ liệu profile từ DTO
   */
  private async validateProfileBlock(profile: any): Promise<void> {
    try {
      this.logger.log('Validating profile block');

      if (!profile || typeof profile !== 'object') {
        throw new AppException(AGENT_ERROR_CODES.INVALID_PROFILE_DATA, 'Profile data phải là object');
      }

      // Validate bio field
      if (profile.bio !== undefined) {
        if (typeof profile.bio !== 'string') {
          throw new AppException(AGENT_ERROR_CODES.INVALID_PROFILE_DATA, 'Bio phải là string');
        }
        if (profile.bio.length > 1000) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_PROFILE_DATA, 'Bio không được vượt quá 1000 ký tự');
        }
      }

      // Validate personality field
      if (profile.personality !== undefined) {
        if (typeof profile.personality !== 'string') {
          throw new AppException(AGENT_ERROR_CODES.INVALID_PROFILE_DATA, 'Personality phải là string');
        }
        if (profile.personality.length > 500) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_PROFILE_DATA, 'Personality không được vượt quá 500 ký tự');
        }
      }

      // Validate expertise field
      if (profile.expertise !== undefined) {
        if (!Array.isArray(profile.expertise)) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_PROFILE_DATA, 'Expertise phải là array');
        }
        if (profile.expertise.length > 10) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_PROFILE_DATA, 'Expertise không được vượt quá 10 items');
        }
        for (const item of profile.expertise) {
          if (typeof item !== 'string' || item.length > 100) {
            throw new AppException(AGENT_ERROR_CODES.INVALID_PROFILE_DATA, 'Mỗi expertise item phải là string và không quá 100 ký tự');
          }
        }
      }

      // Validate language field
      if (profile.language !== undefined) {
        if (typeof profile.language !== 'string') {
          throw new AppException(AGENT_ERROR_CODES.INVALID_PROFILE_DATA, 'Language phải là string');
        }
        const validLanguages = ['vi', 'en', 'zh', 'ja', 'ko', 'fr', 'de', 'es'];
        if (!validLanguages.includes(profile.language)) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_PROFILE_DATA, `Language phải là một trong: ${validLanguages.join(', ')}`);
        }
      }

      this.logger.log('Profile block validation passed');
    } catch (error) {
      this.logger.error(`Profile validation failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Validate Conversion block - Kiểm tra cấu hình chuyển đổi
   * @param conversion Dữ liệu conversion từ DTO
   */
  private async validateConversionBlock(conversion: any): Promise<void> {
    try {
      this.logger.log('Validating conversion block');

      if (!conversion || typeof conversion !== 'object') {
        throw new AppException(AGENT_ERROR_CODES.INVALID_CONVERSION_DATA, 'Conversion data phải là object');
      }

      // Validate format field
      if (conversion.format !== undefined) {
        const validFormats = ['json', 'xml', 'text', 'markdown', 'html'];
        if (!validFormats.includes(conversion.format)) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_CONVERSION_DATA, `Format phải là một trong: ${validFormats.join(', ')}`);
        }
      }

      // Validate template field
      if (conversion.template !== undefined) {
        if (typeof conversion.template !== 'string') {
          throw new AppException(AGENT_ERROR_CODES.INVALID_CONVERSION_DATA, 'Template phải là string');
        }
        if (conversion.template.length > 5000) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_CONVERSION_DATA, 'Template không được vượt quá 5000 ký tự');
        }
      }

      // Validate rules field
      if (conversion.rules !== undefined) {
        if (!Array.isArray(conversion.rules)) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_CONVERSION_DATA, 'Rules phải là array');
        }
        if (conversion.rules.length > 20) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_CONVERSION_DATA, 'Rules không được vượt quá 20 items');
        }
        for (const rule of conversion.rules) {
          if (typeof rule !== 'object' || !rule.condition || !rule.action) {
            throw new AppException(AGENT_ERROR_CODES.INVALID_CONVERSION_DATA, 'Mỗi rule phải có condition và action');
          }
        }
      }

      this.logger.log('Conversion block validation passed');
    } catch (error) {
      this.logger.error(`Conversion validation failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Validate Strategy block - Kiểm tra cấu hình chiến lược
   * @param strategy Dữ liệu strategy từ DTO
   */
  private async validateStrategyBlock(strategy: any): Promise<void> {
    try {
      this.logger.log('Validating strategy block');

      if (!strategy || typeof strategy !== 'object') {
        throw new AppException(AGENT_ERROR_CODES.INVALID_STRATEGY_DATA, 'Strategy data phải là object');
      }

      // Validate strategyId field
      if (strategy.strategyId !== undefined) {
        if (typeof strategy.strategyId !== 'string') {
          throw new AppException(AGENT_ERROR_CODES.INVALID_STRATEGY_DATA, 'Strategy ID phải là string');
        }
        if (strategy.strategyId.trim().length === 0) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_STRATEGY_DATA, 'Strategy ID không được để trống');
        }
        // TODO: Validate strategy exists in database
        // const strategyExists = await this.strategyRepository.existsById(strategy.strategyId);
        // if (!strategyExists) {
        //   throw new AppException(AGENT_ERROR_CODES.STRATEGY_NOT_FOUND, 'Strategy không tồn tại');
        // }
      }

      // Validate parameters field
      if (strategy.parameters !== undefined) {
        if (typeof strategy.parameters !== 'object') {
          throw new AppException(AGENT_ERROR_CODES.INVALID_STRATEGY_DATA, 'Strategy parameters phải là object');
        }
      }

      this.logger.log('Strategy block validation passed');
    } catch (error) {
      this.logger.error(`Strategy validation failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Validate Multi Agent block - Kiểm tra cấu hình multi-agent
   * @param multiAgent Dữ liệu multi agent từ DTO
   */
  private async validateMultiAgentBlock(multiAgent: any): Promise<void> {
    try {
      this.logger.log('Validating multi agent block');

      if (!multiAgent || typeof multiAgent !== 'object') {
        throw new AppException(AGENT_ERROR_CODES.INVALID_MULTI_AGENT_DATA, 'Multi agent data phải là object');
      }

      // Validate collaborationAgentIds field
      if (multiAgent.collaborationAgentIds !== undefined) {
        if (!Array.isArray(multiAgent.collaborationAgentIds)) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_MULTI_AGENT_DATA, 'Collaboration agent IDs phải là array');
        }
        if (multiAgent.collaborationAgentIds.length > 10) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_MULTI_AGENT_DATA, 'Không được vượt quá 10 collaboration agents');
        }

        // Validate each agent ID
        for (const agentId of multiAgent.collaborationAgentIds) {
          if (typeof agentId !== 'string' || agentId.trim().length === 0) {
            throw new AppException(AGENT_ERROR_CODES.INVALID_MULTI_AGENT_DATA, 'Mỗi collaboration agent ID phải là string không rỗng');
          }
        }

        // Check for duplicates
        const uniqueIds = new Set(multiAgent.collaborationAgentIds);
        if (uniqueIds.size !== multiAgent.collaborationAgentIds.length) {
          throw new AppException(AGENT_ERROR_CODES.AGENT_MULTI_AGENT_DUPLICATE, 'Không được có agent ID trùng lặp');
        }
      }

      // Validate workflow field
      if (multiAgent.workflow !== undefined) {
        if (typeof multiAgent.workflow !== 'object') {
          throw new AppException(AGENT_ERROR_CODES.INVALID_MULTI_AGENT_DATA, 'Workflow phải là object');
        }
      }

      this.logger.log('Multi agent block validation passed');
    } catch (error) {
      this.logger.error(`Multi agent validation failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Validate Output Messenger block - Kiểm tra cấu hình đầu ra messenger
   * @param outputMessenger Dữ liệu output messenger từ DTO
   */
  private async validateOutputMessengerBlock(outputMessenger: any): Promise<void> {
    try {
      this.logger.log('Validating output messenger block');

      if (!outputMessenger || typeof outputMessenger !== 'object') {
        throw new AppException(AGENT_ERROR_CODES.INVALID_OUTPUT_MESSENGER_DATA, 'Output messenger data phải là object');
      }

      // Validate facebookPageIds field
      if (outputMessenger.facebookPageIds !== undefined) {
        if (!Array.isArray(outputMessenger.facebookPageIds)) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_OUTPUT_MESSENGER_DATA, 'Facebook page IDs phải là array');
        }
        if (outputMessenger.facebookPageIds.length > 5) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_OUTPUT_MESSENGER_DATA, 'Không được vượt quá 5 Facebook pages');
        }

        // Validate each page ID
        for (const pageId of outputMessenger.facebookPageIds) {
          if (typeof pageId !== 'string' || pageId.trim().length === 0) {
            throw new AppException(AGENT_ERROR_CODES.INVALID_OUTPUT_MESSENGER_DATA, 'Mỗi Facebook page ID phải là string không rỗng');
          }
        }

        // Check for duplicates
        const uniquePageIds = new Set(outputMessenger.facebookPageIds);
        if (uniquePageIds.size !== outputMessenger.facebookPageIds.length) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_OUTPUT_MESSENGER_DATA, 'Không được có Facebook page ID trùng lặp');
        }
      }

      // Validate autoReply field
      if (outputMessenger.autoReply !== undefined) {
        if (typeof outputMessenger.autoReply !== 'boolean') {
          throw new AppException(AGENT_ERROR_CODES.INVALID_OUTPUT_MESSENGER_DATA, 'Auto reply phải là boolean');
        }
      }

      this.logger.log('Output messenger block validation passed');
    } catch (error) {
      this.logger.error(`Output messenger validation failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Validate Output Website block - Kiểm tra cấu hình đầu ra website
   * @param outputWebsite Dữ liệu output website từ DTO
   */
  private async validateOutputWebsiteBlock(outputWebsite: any): Promise<void> {
    try {
      this.logger.log('Validating output website block');

      if (!outputWebsite || typeof outputWebsite !== 'object') {
        throw new AppException(AGENT_ERROR_CODES.INVALID_OUTPUT_WEBSITE_DATA, 'Output website data phải là object');
      }

      // Validate websiteIds field
      if (outputWebsite.websiteIds !== undefined) {
        if (!Array.isArray(outputWebsite.websiteIds)) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_OUTPUT_WEBSITE_DATA, 'Website IDs phải là array');
        }
        if (outputWebsite.websiteIds.length > 10) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_OUTPUT_WEBSITE_DATA, 'Không được vượt quá 10 websites');
        }

        // Validate each website ID
        for (const websiteId of outputWebsite.websiteIds) {
          if (typeof websiteId !== 'string' || websiteId.trim().length === 0) {
            throw new AppException(AGENT_ERROR_CODES.INVALID_OUTPUT_WEBSITE_DATA, 'Mỗi website ID phải là string không rỗng');
          }
        }

        // Check for duplicates
        const uniqueWebsiteIds = new Set(outputWebsite.websiteIds);
        if (uniqueWebsiteIds.size !== outputWebsite.websiteIds.length) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_OUTPUT_WEBSITE_DATA, 'Không được có website ID trùng lặp');
        }
      }

      // Validate embedCode field
      if (outputWebsite.embedCode !== undefined) {
        if (typeof outputWebsite.embedCode !== 'string') {
          throw new AppException(AGENT_ERROR_CODES.INVALID_OUTPUT_WEBSITE_DATA, 'Embed code phải là string');
        }
        if (outputWebsite.embedCode.length > 10000) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_OUTPUT_WEBSITE_DATA, 'Embed code không được vượt quá 10000 ký tự');
        }
      }

      this.logger.log('Output website block validation passed');
    } catch (error) {
      this.logger.error(`Output website validation failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Validate Resources block - Kiểm tra cấu hình tài nguyên
   * @param resources Dữ liệu resources từ DTO
   */
  private async validateResourcesBlock(resources: any): Promise<void> {
    try {
      this.logger.log('Validating resources block');

      if (!resources || typeof resources !== 'object') {
        throw new AppException(AGENT_ERROR_CODES.INVALID_RESOURCES_DATA, 'Resources data phải là object');
      }

      // Validate mediaIds field
      if (resources.mediaIds !== undefined) {
        if (!Array.isArray(resources.mediaIds)) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_RESOURCES_DATA, 'Media IDs phải là array');
        }
        if (resources.mediaIds.length > 50) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_RESOURCES_DATA, 'Không được vượt quá 50 media files');
        }

        // Validate each media ID
        for (const mediaId of resources.mediaIds) {
          if (typeof mediaId !== 'string' || mediaId.trim().length === 0) {
            throw new AppException(AGENT_ERROR_CODES.INVALID_RESOURCES_DATA, 'Mỗi media ID phải là string không rỗng');
          }
        }
      }

      // Validate productIds field
      if (resources.productIds !== undefined) {
        if (!Array.isArray(resources.productIds)) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_RESOURCES_DATA, 'Product IDs phải là array');
        }
        if (resources.productIds.length > 100) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_RESOURCES_DATA, 'Không được vượt quá 100 products');
        }

        // Validate each product ID
        for (const productId of resources.productIds) {
          if (typeof productId !== 'string' || productId.trim().length === 0) {
            throw new AppException(AGENT_ERROR_CODES.INVALID_RESOURCES_DATA, 'Mỗi product ID phải là string không rỗng');
          }
        }
      }

      // Validate urlIds field
      if (resources.urlIds !== undefined) {
        if (!Array.isArray(resources.urlIds)) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_RESOURCES_DATA, 'URL IDs phải là array');
        }
        if (resources.urlIds.length > 20) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_RESOURCES_DATA, 'Không được vượt quá 20 URLs');
        }

        // Validate each URL ID
        for (const urlId of resources.urlIds) {
          if (typeof urlId !== 'string' || urlId.trim().length === 0) {
            throw new AppException(AGENT_ERROR_CODES.INVALID_RESOURCES_DATA, 'Mỗi URL ID phải là string không rỗng');
          }
        }
      }

      // Validate vectorStoreId field
      if (resources.vectorStoreId !== undefined) {
        if (typeof resources.vectorStoreId !== 'string') {
          throw new AppException(AGENT_ERROR_CODES.INVALID_RESOURCES_DATA, 'Vector store ID phải là string');
        }
        if (resources.vectorStoreId.trim().length === 0) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_RESOURCES_DATA, 'Vector store ID không được để trống');
        }
      }

      // Validate customToolIds field
      if (resources.customToolIds !== undefined) {
        if (!Array.isArray(resources.customToolIds)) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_RESOURCES_DATA, 'Custom tool IDs phải là array');
        }
        if (resources.customToolIds.length > 10) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_RESOURCES_DATA, 'Không được vượt quá 10 custom tools');
        }

        // Validate each custom tool ID
        for (const toolId of resources.customToolIds) {
          if (typeof toolId !== 'string' || toolId.trim().length === 0) {
            throw new AppException(AGENT_ERROR_CODES.INVALID_RESOURCES_DATA, 'Mỗi custom tool ID phải là string không rỗng');
          }
        }
      }

      this.logger.log('Resources block validation passed');
    } catch (error) {
      this.logger.error(`Resources validation failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tạo agent entity cơ bản
   * @param createDto DTO tạo agent
   * @param typeAgent TypeAgent entity
   * @param userId ID của user
   * @returns Agent entity đã được lưu
   */
  private async createAgentEntity(
    createDto: CreateAgentDto,
    userId: number,
  ): Promise<Agent> {
    try {

      // Tạo agent entity cơ bản
      const agent = new Agent();
      agent.name = createDto.name;
      agent.modelConfig = createDto.modelConfig;
      agent.instruction = createDto.instruction || null;
      agent.avatar = generateS3Key({
        baseFolder: userId.toString(),
        categoryFolder: CategoryFolderEnum.AGENT,
      });
      agent.vectorStoreId = createDto.vectorStoreId || null;

      // Lưu agent vào database
      const savedAgent = await this.agentRepository.save(agent);

      // Tạo agent user relationship
      const agentUser = new AgentUser();
      agentUser.id = savedAgent.id;
      agentUser.userId = userId;
      agentUser.typeId = createDto.typeId;
      agentUser.profile = createDto.profile ? ProfileMapper.fromDto(createDto.profile) : {};

      // Lưu model fields từ createDto - sử dụng đúng tên trường trong entity
      agentUser.systemModelId = createDto.systemModelId || null;
      agentUser.keyLlmId = createDto.keyLlmId || null;
      agentUser.userModelId = createDto.userModelId || null;
      agentUser.modelFineTuneId = createDto.modelFineTuneId || null;

      // Lưu agent user relationship
      await this.agentUserRepository.save(agentUser);

      this.logger.log(`Đã tạo agent entity: ${savedAgent.id} cho user ${userId}`);
      return savedAgent;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo agent entity: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xử lý các khối cấu hình agent theo TypeAgent config
   * @param agent Agent entity
   * @param createDto Dữ liệu tạo agent
   * @param typeAgentConfig Cấu hình TypeAgent
   * @param userId ID của người dùng
   */
  private async processAgentBlocks(
    agent: Agent,
    createDto: CreateAgentDto,
    typeAgentConfig: TypeAgentConfig,
    userId: number,
  ): Promise<void> {
    try {
      // Xử lý các blocks theo config - chỉ xử lý khi có dữ liệu
      if (typeAgentConfig.enableResourceUsage && createDto.resources) {
        await this.processResourceBlock(agent.id, createDto.resources);
      }

      if (typeAgentConfig.enableDynamicStrategyExecution && createDto.strategy) {
        await this.processStrategyBlock(agent.id, userId, createDto.strategy);
      }

      if (typeAgentConfig.enableMultiAgentCollaboration && createDto.multiAgent) {
        await this.processMultiAgentBlock(agent.id, userId, createDto.multiAgent);
      }

      if (typeAgentConfig.enableOutputToMessenger && createDto.outputMessenger) {
        await this.processOutputMessengerBlock(agent.id, userId, createDto.outputMessenger);
      }

      if (typeAgentConfig.enableOutputToWebsiteLiveChat && createDto.outputWebsite) {
        await this.processOutputWebsiteBlock(agent.id, userId, createDto.outputWebsite);
      }

      this.logger.log(`Đã xử lý các blocks cho agent: ${agent.id}`);
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý agent blocks: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tạo URL upload avatar cho agent
   * @param agentId ID của agent
   * @param mimeType MIME type của avatar
   * @param userId ID của user
   * @returns URL upload avatar
   */
  private async generateAvatarUploadUrl(
    agentId: string,
    mimeType: string,
    userId: number,
  ): Promise<string> {
    try {
      // Tạo S3 key cho avatar
      const avatarKey = generateS3Key({
        baseFolder: userId.toString(),
        categoryFolder: CategoryFolderEnum.AGENT,
      });

      // Tạo presigned URL
      const uploadUrl = await this.s3Service.createPresignedWithID(
        avatarKey,
        TimeIntervalEnum.ONE_HOUR,
        ImageType.getType(mimeType),
        FileSizeEnum.FIVE_MB,
      );

      // Cập nhật avatar key cho agent
      await this.agentRepository.updateAvatar(agentId, avatarKey);

      this.logger.log(`Đã tạo avatar upload URL cho agent: ${agentId}`);
      return uploadUrl;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo avatar upload URL: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.INVALID_S3_KEY, error.message);
    }
  }

  /**
   * Xử lý Resource block - Lưu các tài nguyên vào database
   * @param agentId ID của agent
   * @param resources Dữ liệu resources từ DTO
   */
  private async processResourceBlock(agentId: string, resources: ResourcesBlockDto): Promise<void> {
    try {
      this.logger.log(`Processing resource block for agent ${agentId}`);

      // 1. Xử lý Media IDs
      if (resources.mediaIds && Array.isArray(resources.mediaIds)) {
        for (const mediaId of resources.mediaIds) {
          // Kiểm tra media đã được thêm vào agent chưa
          const existingAgentMedia = await this.agentMediaRepository.findOne({
            where: { agentId, mediaId },
          });

          if (!existingAgentMedia) {
            // Tạo và lưu agent media relationship
            const agentMedia = this.agentMediaRepository.create({
              agentId,
              mediaId,
            });
            await this.agentMediaRepository.save(agentMedia);
            this.logger.debug(`Added media ${mediaId} to agent ${agentId}`);
          }
        }
      }

      // 2. Xử lý Product IDs
      if (resources.productIds && Array.isArray(resources.productIds)) {
        for (const productId of resources.productIds) {
          // Kiểm tra product đã được thêm vào agent chưa
          const existingAgentProduct = await this.agentProductRepository.findOne({
            where: { agentId, productId: productId },
          });

          if (!existingAgentProduct) {
            // Tạo và lưu agent product relationship
            const agentProduct = this.agentProductRepository.create({
              agentId,
              productId: productId,
            });
            await this.agentProductRepository.save(agentProduct);
            this.logger.debug(`Added product ${productId} to agent ${agentId}`);
          }
        }
      }

      // 3. Xử lý URL IDs
      if (resources.urlIds && Array.isArray(resources.urlIds)) {
        for (const urlId of resources.urlIds) {
          // Kiểm tra URL đã được thêm vào agent chưa
          const existingAgentUrl = await this.agentUrlRepository.findOne({
            where: { agentId, urlId },
          });

          if (!existingAgentUrl) {
            // Tạo và lưu agent URL relationship
            const agentUrl = this.agentUrlRepository.create({
              agentId,
              urlId,
            });
            await this.agentUrlRepository.save(agentUrl);
            this.logger.debug(`Added URL ${urlId} to agent ${agentId}`);
          }
        }
      }

      this.logger.log(`Successfully processed resource block for agent ${agentId}`);
    } catch (error) {
      this.logger.error(`Error processing resource block for agent ${agentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Xử lý Strategy block - Cập nhật strategyId trong AgentUser
   * @param agentId ID của agent
   * @param userId ID của user
   * @param strategy Dữ liệu strategy từ DTO
   */
  private async processStrategyBlock(agentId: string, userId: number, strategy: any): Promise<void> {
    try {
      this.logger.log(`Processing strategy block for agent ${agentId}`);

      // Xử lý Strategy ID
      if (strategy.strategyId) {
        // Cập nhật strategyId trong AgentUser
        await this.agentUserRepository.updateStrategyId(agentId, userId, strategy.strategyId);
        this.logger.debug(`Updated strategy ${strategy.strategyId} for agent ${agentId}`);
      }

      this.logger.log(`Successfully processed strategy block for agent ${agentId}`);
    } catch (error) {
      this.logger.error(`Error processing strategy block for agent ${agentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_STRATEGY_NOT_SUPPORTED, error.message);
    }
  }

  /**
   * Xử lý Multi Agent block - Lưu quan hệ hợp tác multi-agent
   * @param agentId ID của agent
   * @param userId ID của user
   * @param multiAgent Dữ liệu multi agent từ DTO
   */
  private async processMultiAgentBlock(agentId: string, userId: number, multiAgent: any): Promise<void> {
    try {
      this.logger.log(`Processing multi agent block for agent ${agentId}`);

      // Xử lý collaboration agent IDs
      if (multiAgent.collaborationAgentIds && Array.isArray(multiAgent.collaborationAgentIds)) {
        if (multiAgent.collaborationAgentIds.length > 0) {
          // Sử dụng repository method để bulk add collaboration agents
          const result = await this.userMultiAgentRepository.bulkAddCollaborationAgents(
            agentId,
            multiAgent.collaborationAgentIds,
            undefined // Có thể thêm prompt từ DTO nếu cần
          );

          this.logger.debug(
            `Bulk add collaboration agents for agent ${agentId}: ` +
            `${result.addedCount} added, ${result.skippedCount} skipped (already exist)`
          );

          if (result.existingIds.length > 0) {
            this.logger.debug(`Existing collaboration agents: [${result.existingIds.join(', ')}]`);
          }
        }
      }

      this.logger.log(`Successfully processed multi agent block for agent ${agentId}`);
    } catch (error) {
      this.logger.error(`Error processing multi agent block for agent ${agentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_MULTI_AGENT_NOT_SUPPORTED, error.message);
    }
  }

  /**
   * Xử lý Output Messenger block - Lưu quan hệ với Facebook Pages
   * @param agentId ID của agent
   * @param userId ID của user
   * @param outputMessenger Dữ liệu output messenger từ DTO
   */
  private async processOutputMessengerBlock(agentId: string, userId: number, outputMessenger: any): Promise<void> {
    try {
      this.logger.log(`Processing output messenger block for agent ${agentId}`);

      // Xử lý Facebook Page IDs
      if (outputMessenger.facebookPageIds && Array.isArray(outputMessenger.facebookPageIds)) {
        for (const pageId of outputMessenger.facebookPageIds) {
          // Kiểm tra Facebook Page có thuộc về user không
          const facebookPage = await this.facebookPageRepository.findPageByUserIdAndPageId(userId, pageId);

          if (facebookPage) {
            // TODO: Tạo relationship giữa agent và Facebook Page
            // Cần tạo AgentOutputMessengerRepository và entity tương ứng
            this.logger.debug(`Added Facebook page ${pageId} to agent ${agentId}`);
          } else {
            this.logger.warn(`Facebook page ${pageId} not found or not owned by user ${userId}`);
          }
        }
      }

      this.logger.log(`Successfully processed output messenger block for agent ${agentId}`);
    } catch (error) {
      this.logger.error(`Error processing output messenger block for agent ${agentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED, error.message);
    }
  }

  /**
   * Xử lý Output Website block - Lưu quan hệ với User Websites
   * @param agentId ID của agent
   * @param userId ID của user
   * @param outputWebsite Dữ liệu output website từ DTO
   */
  private async processOutputWebsiteBlock(agentId: string, userId: number, outputWebsite: any): Promise<void> {
    try {
      this.logger.log(`Processing output website block for agent ${agentId}`);

      // Xử lý Website IDs
      if (outputWebsite.websiteIds && Array.isArray(outputWebsite.websiteIds)) {
        for (const websiteId of outputWebsite.websiteIds) {
          // Kiểm tra Website có thuộc về user không
          const userWebsite = await this.userWebsiteRepository.findByIdAndUserId(websiteId, userId);

          if (userWebsite) {
            // TODO: Tạo relationship giữa agent và User Website
            // Cần tạo AgentOutputWebsiteRepository và entity tương ứng
            this.logger.debug(`Added website ${websiteId} to agent ${agentId}`);
          } else {
            this.logger.warn(`Website ${websiteId} not found or not owned by user ${userId}`);
          }
        }
      }

      this.logger.log(`Successfully processed output website block for agent ${agentId}`);
    } catch (error) {
      this.logger.error(`Error processing output website block for agent ${agentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED, error.message);
    }
  }

  /**
   * Lấy danh sách agent đơn giản của người dùng (chỉ id, avatar, name)
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn với phân trang
   * @returns Danh sách agent đơn giản có phân trang
   */
  async getSimpleAgentList(
    userId: number,
    queryDto: AgentSimpleQueryDto
  ): Promise<PaginatedResult<AgentSimpleListDto>> {
    try {
      // Lấy danh sách agent từ repository với phân trang
      const result = await this.agentRepository.findSimpleListByUserIdPaginated(userId, queryDto);

      // Chuyển đổi avatar thành URL CDN
      const items = result.items.map(item => ({
        id: item.id,
        name: item.name,
        avatar: item.avatar ? this.cdnService.generateUrlView(item.avatar, TimeIntervalEnum.ONE_DAY) || undefined : undefined,
      }));

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách agent đơn giản: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_LIST_QUERY_FAILED, 'Không thể lấy danh sách agent');
    }
  }

  /**
   * Lấy danh sách agent của người dùng
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent có phân trang
   */
  async getAgents(
    userId: number,
    queryDto: AgentQueryDto,
  ): Promise<PaginatedResult<AgentListItemDto>> {
    try {
      // Gọi repository để lấy dữ liệu raw với phân trang
      const result = await this.agentUserRepository.getAgentsList(userId, queryDto);

      // Chuyển đổi raw data sang DTO bằng mapper
      const dtoItems = this.agentListMapper.toDtoList(result.items);

      return {
        items: dtoItems,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách agent: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_LIST_QUERY_FAILED, 'Không thể lấy danh sách agent');
    }
  }

  /**
   * Xóa agent (soft delete)
   * @param agentId ID của agent
   * @param userId ID của người dùng
   */
  @Transactional()
  async deleteAgent(agentId: string, userId: number): Promise<void> {
    try {
      // Kiểm tra agent có tồn tại và thuộc về user không
      const agentExists = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);

      if (!agentExists) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND, 'Agent không tồn tại hoặc không thuộc về bạn');
      }

      // Thực hiện soft delete trên bảng agents
      const deleteResult = await this.agentRepository
        .createQueryBuilder()
        .update()
        .set({
          deletedAt: Date.now()
        })
        .where('id = :agentId', { agentId })
        .execute();

      if (deleteResult.affected === 0) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_DELETE_FAILED, 'Không thể xóa agent');
      }

      this.logger.log(`Agent ${agentId} đã được xóa bởi user ${userId}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa agent ${agentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_DELETE_FAILED, 'Không thể xóa agent');
    }
  }

  /**
   * Đảo ngược trạng thái hoạt động của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @returns Trạng thái mới của agent
   */
  @Transactional()
  async updateAgentActive(agentId: string, userId: number): Promise<{ active: boolean }> {
    try {
      // Kiểm tra agent có tồn tại và thuộc về user không
      const agentData = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);

      if (!agentData) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND, 'Agent không tồn tại hoặc không thuộc về bạn');
      }

      // Đảo ngược trạng thái active
      const newActiveStatus = !agentData.agentUser.active;

      // Cập nhật trạng thái trong bảng agents_user
      const updateResult = await this.agentUserRepository
        .createQueryBuilder()
        .update()
        .set({ active: newActiveStatus })
        .where('id = :agentId', { agentId })
        .andWhere('userId = :userId', { userId })
        .execute();

      if (updateResult.affected === 0) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED, 'Không thể cập nhật trạng thái agent');
      }

      this.logger.log(`Agent ${agentId} active status changed to ${newActiveStatus} by user ${userId}`);

      return { active: newActiveStatus };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật trạng thái agent ${agentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED, 'Không thể cập nhật trạng thái agent');
    }
  }
}
